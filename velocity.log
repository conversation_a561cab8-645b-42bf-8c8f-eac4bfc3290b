2024-12-26 17:05:14,178 - Initializing Velocity, Calling init()...
2024-12-26 17:05:14,178 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 17:05:14,178 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 17:05:14,178 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 17:05:14,178 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2024-12-26 17:05:14,178 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 17:05:14,178 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 17:05:14,181 - Resource<PERSON>oader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 17:05:14,182 - Do unicode file recognition:  false
2024-12-26 17:05:14,182 - FileResourceLoader : adding path '.'
2024-12-26 17:05:14,188 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 17:05:14,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 17:05:14,191 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 17:05:14,191 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 17:05:14,192 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 17:05:14,192 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 17:05:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 17:05:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 17:05:14,194 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 17:05:14,194 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 17:05:14,204 - Created '20' parsers.
2024-12-26 17:05:14,205 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 17:05:14,205 - Velocimacro : Default library not found.
2024-12-26 17:05:14,205 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 17:05:14,206 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 17:05:14,206 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 17:05:14,206 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-27 09:19:40,931 - Log4JLogChute initialized using file 'velocity.log'
2024-12-27 09:19:40,932 - Initializing Velocity, Calling init()...
2024-12-27 09:19:40,932 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-27 09:19:40,932 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-27 09:19:40,932 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-27 09:19:40,932 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2024-12-27 09:19:40,932 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-27 09:19:40,932 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-27 09:19:40,944 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-27 09:19:40,948 - Do unicode file recognition:  false
2024-12-27 09:19:40,948 - FileResourceLoader : adding path '.'
2024-12-27 09:19:41,026 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-27 09:19:41,034 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-27 09:19:41,037 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-27 09:19:41,039 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-27 09:19:41,041 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-27 09:19:41,042 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-27 09:19:41,045 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-27 09:19:41,048 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-27 09:19:41,050 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-27 09:19:41,052 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-27 09:19:41,176 - Created '20' parsers.
2024-12-27 09:19:41,181 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-27 09:19:41,181 - Velocimacro : Default library not found.
2024-12-27 09:19:41,181 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-27 09:19:41,182 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-27 09:19:41,182 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-27 09:19:41,182 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-27 09:19:41,247 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2025-07-29 17:34:39,402 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 17:34:39,402 - Initializing Velocity, Calling init()...
2025-07-29 17:34:39,402 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 17:34:39,402 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 17:34:39,402 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 17:34:39,403 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-29 17:34:39,403 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 17:34:39,403 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 17:34:39,406 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 17:34:39,406 - Do unicode file recognition:  false
2025-07-29 17:34:39,406 - FileResourceLoader : adding path '.'
2025-07-29 17:34:39,415 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 17:34:39,418 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 17:34:39,419 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 17:34:39,419 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 17:34:39,420 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 17:34:39,420 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 17:34:39,421 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 17:34:39,422 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 17:34:39,422 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 17:34:39,423 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 17:34:39,435 - Created '20' parsers.
2025-07-29 17:34:39,437 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 17:34:39,437 - Velocimacro : Default library not found.
2025-07-29 17:34:39,437 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 17:34:39,437 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 17:34:39,437 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 17:34:39,437 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-31 17:26:02,253 - Log4JLogChute initialized using file 'velocity.log'
2025-07-31 17:26:02,254 - Initializing Velocity, Calling init()...
2025-07-31 17:26:02,254 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-31 17:26:02,254 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-31 17:26:02,254 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-31 17:26:02,254 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-31 17:26:02,254 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-31 17:26:02,254 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-31 17:26:02,257 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-31 17:26:02,258 - Do unicode file recognition:  false
2025-07-31 17:26:02,258 - FileResourceLoader : adding path '.'
2025-07-31 17:26:02,272 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-31 17:26:02,274 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-31 17:26:02,275 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-31 17:26:02,276 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-31 17:26:02,276 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-31 17:26:02,277 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-31 17:26:02,278 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-31 17:26:02,278 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-31 17:26:02,279 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-31 17:26:02,280 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-31 17:26:02,290 - Created '20' parsers.
2025-07-31 17:26:02,292 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-31 17:26:02,292 - Velocimacro : Default library not found.
2025-07-31 17:26:02,292 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-31 17:26:02,292 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-31 17:26:02,292 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-31 17:26:02,293 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-08-01 17:32:05,406 - Log4JLogChute initialized using file 'velocity.log'
2025-08-01 17:32:05,407 - Initializing Velocity, Calling init()...
2025-08-01 17:32:05,407 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-01 17:32:05,407 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-01 17:32:05,408 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-01 17:32:05,408 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-01 17:32:05,408 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-01 17:32:05,408 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-01 17:32:05,412 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-08-01 17:32:05,413 - Do unicode file recognition:  false
2025-08-01 17:32:05,414 - FileResourceLoader : adding path '.'
2025-08-01 17:32:05,431 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-01 17:32:05,434 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-01 17:32:05,435 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-01 17:32:05,436 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-01 17:32:05,437 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-01 17:32:05,438 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-01 17:32:05,438 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-01 17:32:05,440 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-01 17:32:05,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-01 17:32:05,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-01 17:32:05,456 - Created '20' parsers.
2025-08-01 17:32:05,459 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-01 17:32:05,459 - Velocimacro : Default library not found.
2025-08-01 17:32:05,459 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-01 17:32:05,459 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-01 17:32:05,459 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-01 17:32:05,459 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-08-02 12:56:40,280 - Log4JLogChute initialized using file 'velocity.log'
2025-08-02 12:56:40,281 - Initializing Velocity, Calling init()...
2025-08-02 12:56:40,281 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-02 12:56:40,281 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-02 12:56:40,281 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-02 12:56:40,281 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-02 12:56:40,281 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-02 12:56:40,281 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-02 12:56:40,285 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-08-02 12:56:40,286 - Do unicode file recognition:  false
2025-08-02 12:56:40,286 - FileResourceLoader : adding path '.'
2025-08-02 12:56:40,307 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-02 12:56:40,310 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-02 12:56:40,311 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-02 12:56:40,312 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-02 12:56:40,312 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-02 12:56:40,313 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-02 12:56:40,313 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-02 12:56:40,314 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-02 12:56:40,315 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-02 12:56:40,316 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-02 12:56:40,329 - Created '20' parsers.
2025-08-02 12:56:40,332 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-02 12:56:40,332 - Velocimacro : Default library not found.
2025-08-02 12:56:40,332 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-02 12:56:40,332 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-02 12:56:40,332 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-02 12:56:40,332 - Velocimacro : autoload off : VM system will not automatically reload global library macros
