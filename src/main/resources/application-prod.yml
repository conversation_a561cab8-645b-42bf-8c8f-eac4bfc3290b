server:
  port: 10656 #服务端口
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  datasource:
    #MYsql连接字符串
    url: *************************************************************************************************************************************************
    username: root
    password: asd@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    maximum-pool-size: 10
    hikari:
      connection-test-query: SELECT 1
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: *************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
  mail:
    #      username: <EMAIL>
    #      password: fhaaqtqoxlhdddjc
    username: <EMAIL> # 配置邮箱用户名(你的邮箱地址)
    password: pcwwnfnesudmbbfd # 配置申请到的授权码
    host: smtp.qq.com
    properties:
      mail.socketFactoryClass: javax.net.ssl.SSLSocketFactory
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.starttls.required: true
      debug: true
    default-encoding: UTF-8
    toUser: <EMAIL>    #收件人
    fromUser: inks     #发件人名称
    subject: 需求单更新提醒  #邮件主题
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.store.**.domain
  #配置打印SQL语句到控制台
logging:
  level:
    org:
      springframework:
        security: info

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
debug: true

wechat:
  miniapp:
    config:
      #微信小程序的appid
      appid: wx3866e1b5f1524d0f
      #微信小程序的Secret
      secret: 27158463ac80520f28ebca7c79a9ae57
      #微信小程序消息服务器配置的token
      token: 123456
      #微信小程序消息服务器配置的EncodingAESKey
      aseKey: 123456
      msgDataFormat: JSON


oss:
  bucket: inkspms
  minio:
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://dev.inksyun.com:9080
# 代码生成
gen:
  # 作者
  author: author-inks
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.ruoyi.system
  # 自动去除表前缀，默认是false
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_