2024-12-25 17:05:01,628 - Initializing Velocity, Calling init()...
2024-12-25 17:05:01,628 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:05:01,628 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:05:01,628 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:05:01,628 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2024-12-25 17:05:01,628 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:05:01,629 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:05:01,640 - <PERSON><PERSON>oader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:05:01,643 - Do unicode file recognition:  false
2024-12-25 17:05:01,643 - FileResourceLoader : adding path '.'
2024-12-25 17:05:01,719 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:05:01,727 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:05:01,730 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:05:01,732 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:05:01,733 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:05:01,735 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:05:01,738 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:05:01,740 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:05:01,744 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:05:01,747 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:05:01,871 - Created '20' parsers.
2024-12-25 17:05:01,878 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:05:01,878 - Velocimacro : Default library not found.
2024-12-25 17:05:01,878 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:05:01,878 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:05:01,879 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:05:01,879 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:05:01,942 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:11:04,701 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:11:04,701 - Initializing Velocity, Calling init()...
2024-12-25 17:11:04,701 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:11:04,701 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:11:04,701 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:11:04,702 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:11:04,702 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:11:04,702 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:11:04,702 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:11:04,702 - Do unicode file recognition:  false
2024-12-25 17:11:04,702 - FileResourceLoader : adding path '.'
2024-12-25 17:11:04,702 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:11:04,702 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:11:04,702 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:11:04,702 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:11:04,702 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:11:04,702 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:11:04,702 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:11:04,702 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:11:04,703 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:11:04,703 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:11:04,703 - Created '20' parsers.
2024-12-25 17:11:04,703 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:11:04,703 - Velocimacro : Default library not found.
2024-12-25 17:11:04,703 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:11:04,704 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:11:04,704 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:11:04,704 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:11:04,706 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
allowed.
2024-12-25 17:11:04,704 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:11:04,706 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:22:04,997 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:22:04,999 - Initializing Velocity, Calling init()...
2024-12-25 17:22:04,999 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:22:04,999 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:22:04,999 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:22:04,999 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:22:04,999 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:22:04,999 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:22:04,999 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:22:04,999 - Do unicode file recognition:  false
2024-12-25 17:22:04,999 - FileResourceLoader : adding path '.'
2024-12-25 17:22:04,999 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:22:04,999 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:22:04,999 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:22:04,999 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:22:04,999 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:22:05,000 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:22:05,000 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:22:05,000 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:22:05,000 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:22:05,000 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:22:05,000 - Created '20' parsers.
2024-12-25 17:22:05,000 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:22:05,001 - Velocimacro : Default library not found.
2024-12-25 17:22:05,001 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:22:05,001 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:22:05,001 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:22:05,001 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:25:07,127 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:25:07,127 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:25:07,128 - Initializing Velocity, Calling init()...
2024-12-25 17:25:07,128 - Initializing Velocity, Calling init()...
2024-12-25 17:25:07,128 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:25:07,128 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:25:07,128 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:25:07,128 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:25:07,128 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:25:07,128 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:25:07,128 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:25:07,128 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:25:07,128 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:07,128 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:07,128 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:07,128 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:07,128 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:25:07,128 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:25:07,128 - Do unicode file recognition:  false
2024-12-25 17:25:07,128 - Do unicode file recognition:  false
2024-12-25 17:25:07,128 - FileResourceLoader : adding path '.'
2024-12-25 17:25:07,128 - FileResourceLoader : adding path '.'
2024-12-25 17:25:07,128 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:25:07,128 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:25:07,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:25:07,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:25:07,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:25:07,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:25:07,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:25:07,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:25:07,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:25:07,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:25:07,129 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:25:07,129 - Created '20' parsers.
2024-12-25 17:25:07,129 - Created '20' parsers.
2024-12-25 17:25:07,129 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:25:07,129 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:25:07,130 - Velocimacro : Default library not found.
2024-12-25 17:25:07,130 - Velocimacro : Default library not found.
2024-12-25 17:25:07,130 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:25:07,130 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:25:07,130 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:25:07,130 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:25:07,130 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:25:07,130 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:25:07,130 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:25:07,130 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:25:07,132 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:25:07,132 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:25:33,382 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:25:33,382 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:25:33,382 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:25:33,383 - Initializing Velocity, Calling init()...
2024-12-25 17:25:33,383 - Initializing Velocity, Calling init()...
2024-12-25 17:25:33,383 - Initializing Velocity, Calling init()...
2024-12-25 17:25:33,383 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:25:33,383 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:25:33,383 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:25:33,383 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:25:33,383 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:25:33,383 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:25:33,383 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:25:33,383 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:25:33,383 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:25:33,383 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:25:33,383 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:25:33,383 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:25:33,383 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:33,383 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:33,383 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:33,383 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:33,383 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:33,383 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:25:33,383 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:25:33,383 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:25:33,383 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:25:33,383 - Do unicode file recognition:  false
2024-12-25 17:25:33,383 - Do unicode file recognition:  false
2024-12-25 17:25:33,383 - Do unicode file recognition:  false
2024-12-25 17:25:33,384 - FileResourceLoader : adding path '.'
2024-12-25 17:25:33,384 - FileResourceLoader : adding path '.'
2024-12-25 17:25:33,384 - FileResourceLoader : adding path '.'
2024-12-25 17:25:33,384 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:25:33,384 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:25:33,384 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:25:33,384 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:25:33,385 - Created '20' parsers.
2024-12-25 17:25:33,385 - Created '20' parsers.
2024-12-25 17:25:33,385 - Created '20' parsers.
2024-12-25 17:25:33,385 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:25:33,385 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:25:33,385 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:25:33,385 - Velocimacro : Default library not found.
2024-12-25 17:25:33,385 - Velocimacro : Default library not found.
2024-12-25 17:25:33,385 - Velocimacro : Default library not found.
2024-12-25 17:25:33,385 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:25:33,385 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:25:33,385 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:25:33,385 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:25:33,385 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:25:33,385 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:25:33,385 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:25:33,385 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:25:33,385 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:25:33,385 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:25:33,385 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:25:33,385 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:25:33,387 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:25:33,387 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:25:33,387 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:26:10,849 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:26:10,849 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:26:10,849 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:26:10,849 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:26:10,850 - Initializing Velocity, Calling init()...
2024-12-25 17:26:10,850 - Initializing Velocity, Calling init()...
2024-12-25 17:26:10,850 - Initializing Velocity, Calling init()...
2024-12-25 17:26:10,850 - Initializing Velocity, Calling init()...
2024-12-25 17:26:10,850 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:26:10,850 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:26:10,850 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:26:10,850 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:26:10,850 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:26:10,850 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:26:10,850 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:26:10,850 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:26:10,850 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:26:10,850 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:26:10,850 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:26:10,850 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:26:10,850 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:26:10,850 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:26:10,850 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:26:10,850 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:26:10,850 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:26:10,850 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:26:10,850 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:26:10,850 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:26:10,850 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:26:10,850 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:26:10,850 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:26:10,850 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:26:10,850 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:26:10,850 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:26:10,850 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:26:10,850 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:26:10,850 - Do unicode file recognition:  false
2024-12-25 17:26:10,850 - Do unicode file recognition:  false
2024-12-25 17:26:10,850 - Do unicode file recognition:  false
2024-12-25 17:26:10,850 - Do unicode file recognition:  false
2024-12-25 17:26:10,850 - FileResourceLoader : adding path '.'
2024-12-25 17:26:10,850 - FileResourceLoader : adding path '.'
2024-12-25 17:26:10,850 - FileResourceLoader : adding path '.'
2024-12-25 17:26:10,850 - FileResourceLoader : adding path '.'
2024-12-25 17:26:10,850 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:26:10,850 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:26:10,850 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:26:10,850 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:26:10,851 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:26:10,851 - Created '20' parsers.
2024-12-25 17:26:10,851 - Created '20' parsers.
2024-12-25 17:26:10,851 - Created '20' parsers.
2024-12-25 17:26:10,851 - Created '20' parsers.
2024-12-25 17:26:10,851 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:26:10,851 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:26:10,851 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:26:10,851 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:26:10,852 - Velocimacro : Default library not found.
2024-12-25 17:26:10,852 - Velocimacro : Default library not found.
2024-12-25 17:26:10,852 - Velocimacro : Default library not found.
2024-12-25 17:26:10,852 - Velocimacro : Default library not found.
2024-12-25 17:26:10,852 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:26:10,852 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:26:10,852 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:26:10,852 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:26:10,852 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:26:10,852 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:26:10,852 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:26:10,852 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:26:10,852 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:26:10,852 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:26:10,852 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:26:10,852 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:26:10,852 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:26:10,852 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:26:10,852 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:26:10,852 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:26:10,854 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:26:10,854 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:26:10,854 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:26:10,854 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:27:46,302 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:27:46,302 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:27:46,302 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:27:46,302 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:27:46,302 - Log4JLogChute initialized using file 'velocity.log'
2024-12-25 17:27:46,303 - Initializing Velocity, Calling init()...
2024-12-25 17:27:46,303 - Initializing Velocity, Calling init()...
2024-12-25 17:27:46,303 - Initializing Velocity, Calling init()...
2024-12-25 17:27:46,303 - Initializing Velocity, Calling init()...
2024-12-25 17:27:46,303 - Initializing Velocity, Calling init()...
2024-12-25 17:27:46,303 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:27:46,303 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:27:46,303 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:27:46,303 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:27:46,303 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-25 17:27:46,303 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:27:46,303 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:27:46,303 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:27:46,303 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:27:46,303 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-25 17:27:46,303 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:27:46,303 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:27:46,303 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:27:46,303 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:27:46,303 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,303 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-25 17:27:46,304 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:27:46,304 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:27:46,304 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:27:46,304 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:27:46,304 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-25 17:27:46,304 - Do unicode file recognition:  false
2024-12-25 17:27:46,304 - Do unicode file recognition:  false
2024-12-25 17:27:46,304 - Do unicode file recognition:  false
2024-12-25 17:27:46,304 - Do unicode file recognition:  false
2024-12-25 17:27:46,304 - Do unicode file recognition:  false
2024-12-25 17:27:46,304 - FileResourceLoader : adding path '.'
2024-12-25 17:27:46,304 - FileResourceLoader : adding path '.'
2024-12-25 17:27:46,304 - FileResourceLoader : adding path '.'
2024-12-25 17:27:46,304 - FileResourceLoader : adding path '.'
2024-12-25 17:27:46,304 - FileResourceLoader : adding path '.'
2024-12-25 17:27:46,304 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:27:46,304 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:27:46,304 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:27:46,304 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:27:46,304 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:27:46,304 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:27:46,305 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-25 17:27:46,306 - Created '20' parsers.
2024-12-25 17:27:46,306 - Created '20' parsers.
2024-12-25 17:27:46,306 - Created '20' parsers.
2024-12-25 17:27:46,306 - Created '20' parsers.
2024-12-25 17:27:46,306 - Created '20' parsers.
2024-12-25 17:27:46,306 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:27:46,306 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:27:46,306 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:27:46,306 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:27:46,306 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-25 17:27:46,306 - Velocimacro : Default library not found.
2024-12-25 17:27:46,306 - Velocimacro : Default library not found.
2024-12-25 17:27:46,306 - Velocimacro : Default library not found.
2024-12-25 17:27:46,306 - Velocimacro : Default library not found.
2024-12-25 17:27:46,306 - Velocimacro : Default library not found.
2024-12-25 17:27:46,306 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:27:46,306 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:27:46,306 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:27:46,306 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:27:46,306 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-25 17:27:46,306 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:27:46,306 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:27:46,306 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:27:46,306 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:27:46,306 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-25 17:27:46,306 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:27:46,306 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:27:46,306 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:27:46,306 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:27:46,306 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-25 17:27:46,306 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:27:46,306 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:27:46,306 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:27:46,306 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:27:46,306 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-25 17:27:46,308 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:27:46,308 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:27:46,308 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:27:46,308 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-25 17:27:46,308 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-26 16:24:07,637 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:24:07,638 - Initializing Velocity, Calling init()...
2024-12-26 16:24:07,638 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:24:07,638 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:24:07,638 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:24:07,638 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2024-12-26 16:24:07,638 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:24:07,638 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:24:07,651 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:24:07,655 - Do unicode file recognition:  false
2024-12-26 16:24:07,655 - FileResourceLoader : adding path '.'
2024-12-26 16:24:07,739 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:24:07,748 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:24:07,751 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:24:07,753 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:24:07,755 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:24:07,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:24:07,760 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:24:07,763 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:24:07,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:24:07,768 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:24:07,901 - Created '20' parsers.
2024-12-26 16:24:07,906 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:24:07,907 - Velocimacro : Default library not found.
2024-12-26 16:24:07,907 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:24:07,907 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:24:07,907 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:24:07,907 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:24:07,989 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-26 16:26:40,554 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:26:40,556 - Initializing Velocity, Calling init()...
2024-12-26 16:26:40,556 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:26:40,556 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:26:40,556 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:26:40,556 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2024-12-26 16:26:40,556 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:26:40,556 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:26:40,569 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:26:40,572 - Do unicode file recognition:  false
2024-12-26 16:26:40,572 - FileResourceLoader : adding path '.'
2024-12-26 16:26:40,652 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:26:40,659 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:26:40,662 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:26:40,664 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:26:40,665 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:26:40,667 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:26:40,669 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:26:40,672 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:26:40,674 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:26:40,676 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:26:40,790 - Created '20' parsers.
2024-12-26 16:26:40,795 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:26:40,795 - Velocimacro : Default library not found.
2024-12-26 16:26:40,795 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:26:40,795 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:26:40,795 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:26:40,795 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:26:40,857 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-26 16:32:34,861 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:32:34,863 - Initializing Velocity, Calling init()...
2024-12-26 16:32:34,863 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:32:34,863 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:32:34,863 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:32:34,863 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2024-12-26 16:32:34,863 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:32:34,863 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:32:34,874 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:32:34,877 - Do unicode file recognition:  false
2024-12-26 16:32:34,877 - FileResourceLoader : adding path '.'
2024-12-26 16:32:34,952 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:32:34,959 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:32:34,962 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:32:34,964 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:32:34,966 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:32:34,967 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:32:34,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:32:34,973 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:32:34,975 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:32:34,977 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:32:35,104 - Created '20' parsers.
2024-12-26 16:32:35,110 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:32:35,111 - Velocimacro : Default library not found.
2024-12-26 16:32:35,111 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:32:35,111 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:32:35,111 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:32:35,111 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:32:35,185 - Null reference [template '', line 30, column 20] : ${approvePojo.object.groupname} cannot be resolved.
2024-12-26 16:34:57,798 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:34:57,798 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:34:57,799 - Initializing Velocity, Calling init()...
2024-12-26 16:34:57,799 - Initializing Velocity, Calling init()...
2024-12-26 16:34:57,799 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:34:57,799 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:34:57,799 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:34:57,799 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:34:57,799 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:34:57,799 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:34:57,799 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:34:57,799 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:34:57,799 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:34:57,799 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:34:57,799 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:34:57,799 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:34:57,799 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:34:57,799 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:34:57,799 - Do unicode file recognition:  false
2024-12-26 16:34:57,799 - Do unicode file recognition:  false
2024-12-26 16:34:57,799 - FileResourceLoader : adding path '.'
2024-12-26 16:34:57,799 - FileResourceLoader : adding path '.'
2024-12-26 16:34:57,799 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:34:57,799 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:34:57,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:34:57,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:34:57,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:34:57,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:34:57,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:34:57,799 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:34:57,800 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:34:57,801 - Created '20' parsers.
2024-12-26 16:34:57,801 - Created '20' parsers.
2024-12-26 16:34:57,801 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:34:57,801 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:34:57,801 - Velocimacro : Default library not found.
2024-12-26 16:34:57,801 - Velocimacro : Default library not found.
2024-12-26 16:34:57,801 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:34:57,801 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:34:57,801 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:34:57,801 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:34:57,801 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:34:57,801 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:34:57,801 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:34:57,801 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:43:14,358 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:43:14,358 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:43:14,358 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:43:14,359 - Initializing Velocity, Calling init()...
2024-12-26 16:43:14,359 - Initializing Velocity, Calling init()...
2024-12-26 16:43:14,359 - Initializing Velocity, Calling init()...
2024-12-26 16:43:14,359 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:43:14,359 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:43:14,359 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:43:14,359 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:43:14,359 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:43:14,359 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:43:14,359 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:43:14,359 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:43:14,359 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:43:14,359 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:43:14,359 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:43:14,359 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:43:14,359 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:43:14,359 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:43:14,359 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:43:14,359 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:43:14,359 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:43:14,359 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:43:14,359 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:43:14,359 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:43:14,359 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:43:14,359 - Do unicode file recognition:  false
2024-12-26 16:43:14,359 - Do unicode file recognition:  false
2024-12-26 16:43:14,359 - Do unicode file recognition:  false
2024-12-26 16:43:14,359 - FileResourceLoader : adding path '.'
2024-12-26 16:43:14,359 - FileResourceLoader : adding path '.'
2024-12-26 16:43:14,359 - FileResourceLoader : adding path '.'
2024-12-26 16:43:14,360 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:43:14,360 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:43:14,360 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:43:14,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:43:14,361 - Created '20' parsers.
2024-12-26 16:43:14,361 - Created '20' parsers.
2024-12-26 16:43:14,361 - Created '20' parsers.
2024-12-26 16:43:14,361 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:43:14,361 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:43:14,361 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:43:14,361 - Velocimacro : Default library not found.
2024-12-26 16:43:14,361 - Velocimacro : Default library not found.
2024-12-26 16:43:14,361 - Velocimacro : Default library not found.
2024-12-26 16:43:14,362 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:43:14,362 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:43:14,362 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:43:14,362 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:43:14,362 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:43:14,362 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:43:14,362 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:43:14,362 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:43:14,362 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:43:14,362 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:43:14,362 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:43:14,362 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:46:01,501 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:46:01,501 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:46:01,501 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:46:01,501 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:46:01,502 - Initializing Velocity, Calling init()...
2024-12-26 16:46:01,502 - Initializing Velocity, Calling init()...
2024-12-26 16:46:01,502 - Initializing Velocity, Calling init()...
2024-12-26 16:46:01,502 - Initializing Velocity, Calling init()...
2024-12-26 16:46:01,502 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:46:01,502 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:46:01,502 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:46:01,502 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:46:01,502 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:46:01,502 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:46:01,502 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:46:01,502 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:46:01,502 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:46:01,502 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:46:01,502 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:46:01,502 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:46:01,502 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:46:01,502 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:46:01,502 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:46:01,502 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:46:01,502 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:46:01,502 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:46:01,502 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:46:01,502 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:46:01,502 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:46:01,502 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:46:01,502 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:46:01,502 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:46:01,502 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:46:01,502 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:46:01,502 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:46:01,502 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:46:01,502 - Do unicode file recognition:  false
2024-12-26 16:46:01,502 - Do unicode file recognition:  false
2024-12-26 16:46:01,502 - Do unicode file recognition:  false
2024-12-26 16:46:01,502 - Do unicode file recognition:  false
2024-12-26 16:46:01,502 - FileResourceLoader : adding path '.'
2024-12-26 16:46:01,502 - FileResourceLoader : adding path '.'
2024-12-26 16:46:01,502 - FileResourceLoader : adding path '.'
2024-12-26 16:46:01,502 - FileResourceLoader : adding path '.'
2024-12-26 16:46:01,502 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:46:01,502 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:46:01,502 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:46:01,502 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:46:01,503 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:46:01,504 - Created '20' parsers.
2024-12-26 16:46:01,504 - Created '20' parsers.
2024-12-26 16:46:01,504 - Created '20' parsers.
2024-12-26 16:46:01,504 - Created '20' parsers.
2024-12-26 16:46:01,504 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:46:01,504 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:46:01,504 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:46:01,504 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:46:01,504 - Velocimacro : Default library not found.
2024-12-26 16:46:01,504 - Velocimacro : Default library not found.
2024-12-26 16:46:01,504 - Velocimacro : Default library not found.
2024-12-26 16:46:01,504 - Velocimacro : Default library not found.
2024-12-26 16:46:01,504 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:46:01,504 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:46:01,504 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:46:01,504 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:46:01,504 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:46:01,504 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:46:01,504 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:46:01,504 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:46:01,504 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:46:01,504 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:46:01,504 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:46:01,504 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:46:01,504 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:46:01,504 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:46:01,504 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:46:01,504 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:47:19,924 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:47:19,924 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:47:19,924 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:47:19,924 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:47:19,924 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:47:19,925 - Initializing Velocity, Calling init()...
2024-12-26 16:47:19,925 - Initializing Velocity, Calling init()...
2024-12-26 16:47:19,925 - Initializing Velocity, Calling init()...
2024-12-26 16:47:19,925 - Initializing Velocity, Calling init()...
2024-12-26 16:47:19,925 - Initializing Velocity, Calling init()...
2024-12-26 16:47:19,925 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:47:19,925 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:47:19,925 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:47:19,925 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:47:19,925 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:47:19,925 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:47:19,925 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:47:19,925 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:47:19,925 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:47:19,925 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:47:19,925 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:47:19,925 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:47:19,925 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:47:19,925 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:47:19,925 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,925 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:47:19,926 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:47:19,926 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:47:19,926 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:47:19,926 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:47:19,926 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:47:19,926 - Do unicode file recognition:  false
2024-12-26 16:47:19,926 - Do unicode file recognition:  false
2024-12-26 16:47:19,926 - Do unicode file recognition:  false
2024-12-26 16:47:19,926 - Do unicode file recognition:  false
2024-12-26 16:47:19,926 - Do unicode file recognition:  false
2024-12-26 16:47:19,926 - FileResourceLoader : adding path '.'
2024-12-26 16:47:19,926 - FileResourceLoader : adding path '.'
2024-12-26 16:47:19,926 - FileResourceLoader : adding path '.'
2024-12-26 16:47:19,926 - FileResourceLoader : adding path '.'
2024-12-26 16:47:19,926 - FileResourceLoader : adding path '.'
2024-12-26 16:47:19,926 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:47:19,926 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:47:19,926 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:47:19,926 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:47:19,926 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:47:19,926 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:47:19,927 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:47:19,928 - Created '20' parsers.
2024-12-26 16:47:19,928 - Created '20' parsers.
2024-12-26 16:47:19,928 - Created '20' parsers.
2024-12-26 16:47:19,928 - Created '20' parsers.
2024-12-26 16:47:19,928 - Created '20' parsers.
2024-12-26 16:47:19,928 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:47:19,928 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:47:19,928 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:47:19,928 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:47:19,928 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:47:19,928 - Velocimacro : Default library not found.
2024-12-26 16:47:19,928 - Velocimacro : Default library not found.
2024-12-26 16:47:19,928 - Velocimacro : Default library not found.
2024-12-26 16:47:19,928 - Velocimacro : Default library not found.
2024-12-26 16:47:19,928 - Velocimacro : Default library not found.
2024-12-26 16:47:19,928 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:47:19,928 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:47:19,928 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:47:19,928 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:47:19,928 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:47:19,928 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:47:19,928 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:47:19,928 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:47:19,928 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:47:19,928 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:47:19,928 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:47:19,928 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:47:19,928 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:47:19,928 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:47:19,928 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:47:19,928 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:47:19,928 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:47:19,928 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:47:19,928 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:47:19,928 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:48:50,485 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:48:50,485 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:48:50,485 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:48:50,485 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:48:50,485 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:48:50,485 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:48:50,487 - Initializing Velocity, Calling init()...
2024-12-26 16:48:50,487 - Initializing Velocity, Calling init()...
2024-12-26 16:48:50,487 - Initializing Velocity, Calling init()...
2024-12-26 16:48:50,487 - Initializing Velocity, Calling init()...
2024-12-26 16:48:50,487 - Initializing Velocity, Calling init()...
2024-12-26 16:48:50,487 - Initializing Velocity, Calling init()...
2024-12-26 16:48:50,487 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:48:50,487 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:48:50,487 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:48:50,487 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:48:50,487 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:48:50,487 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:48:50,488 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:48:50,488 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:48:50,488 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:48:50,488 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:48:50,488 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:48:50,488 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:48:50,488 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:48:50,488 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:48:50,488 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:48:50,488 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:48:50,488 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:48:50,488 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:48:50,488 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:48:50,488 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:48:50,488 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:48:50,488 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:48:50,488 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:48:50,488 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:48:50,488 - Do unicode file recognition:  false
2024-12-26 16:48:50,488 - Do unicode file recognition:  false
2024-12-26 16:48:50,488 - Do unicode file recognition:  false
2024-12-26 16:48:50,488 - Do unicode file recognition:  false
2024-12-26 16:48:50,488 - Do unicode file recognition:  false
2024-12-26 16:48:50,488 - Do unicode file recognition:  false
2024-12-26 16:48:50,488 - FileResourceLoader : adding path '.'
2024-12-26 16:48:50,488 - FileResourceLoader : adding path '.'
2024-12-26 16:48:50,488 - FileResourceLoader : adding path '.'
2024-12-26 16:48:50,488 - FileResourceLoader : adding path '.'
2024-12-26 16:48:50,488 - FileResourceLoader : adding path '.'
2024-12-26 16:48:50,488 - FileResourceLoader : adding path '.'
2024-12-26 16:48:50,488 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:48:50,488 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:48:50,488 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:48:50,488 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:48:50,488 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:48:50,488 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:48:50,488 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:48:50,489 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:48:50,490 - Created '20' parsers.
2024-12-26 16:48:50,490 - Created '20' parsers.
2024-12-26 16:48:50,490 - Created '20' parsers.
2024-12-26 16:48:50,490 - Created '20' parsers.
2024-12-26 16:48:50,490 - Created '20' parsers.
2024-12-26 16:48:50,490 - Created '20' parsers.
2024-12-26 16:48:50,490 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:48:50,490 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:48:50,490 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:48:50,490 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:48:50,490 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:48:50,490 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:48:50,490 - Velocimacro : Default library not found.
2024-12-26 16:48:50,490 - Velocimacro : Default library not found.
2024-12-26 16:48:50,490 - Velocimacro : Default library not found.
2024-12-26 16:48:50,490 - Velocimacro : Default library not found.
2024-12-26 16:48:50,490 - Velocimacro : Default library not found.
2024-12-26 16:48:50,490 - Velocimacro : Default library not found.
2024-12-26 16:48:50,490 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:48:50,490 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:48:50,490 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:48:50,490 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:48:50,490 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:48:50,490 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:48:50,490 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:48:50,490 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:48:50,490 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:48:50,490 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:48:50,490 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:48:50,490 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:48:50,490 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:48:50,490 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:48:50,490 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:48:50,490 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:48:50,490 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:48:50,490 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:48:50,490 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:48:50,490 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:48:50,490 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:48:50,490 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:48:50,490 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:48:50,490 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:53:51,648 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:53:51,648 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:53:51,648 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:53:51,648 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:53:51,648 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:53:51,648 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:53:51,648 - Log4JLogChute initialized using file 'velocity.log'
2024-12-26 16:53:51,649 - Initializing Velocity, Calling init()...
2024-12-26 16:53:51,649 - Initializing Velocity, Calling init()...
2024-12-26 16:53:51,649 - Initializing Velocity, Calling init()...
2024-12-26 16:53:51,649 - Initializing Velocity, Calling init()...
2024-12-26 16:53:51,649 - Initializing Velocity, Calling init()...
2024-12-26 16:53:51,649 - Initializing Velocity, Calling init()...
2024-12-26 16:53:51,649 - Initializing Velocity, Calling init()...
2024-12-26 16:53:51,649 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:53:51,649 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:53:51,649 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:53:51,649 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:53:51,649 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:53:51,649 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:53:51,649 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2024-12-26 16:53:51,649 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:53:51,649 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:53:51,649 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:53:51,649 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:53:51,649 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:53:51,649 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:53:51,649 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2024-12-26 16:53:51,650 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:53:51,650 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:53:51,650 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:53:51,650 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:53:51,650 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:53:51,650 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:53:51,650 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2024-12-26 16:53:51,650 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:53:51,650 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:53:51,650 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:53:51,650 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:53:51,650 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:53:51,650 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:53:51,650 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2024-12-26 16:53:51,650 - Do unicode file recognition:  false
2024-12-26 16:53:51,650 - Do unicode file recognition:  false
2024-12-26 16:53:51,650 - Do unicode file recognition:  false
2024-12-26 16:53:51,650 - Do unicode file recognition:  false
2024-12-26 16:53:51,650 - Do unicode file recognition:  false
2024-12-26 16:53:51,650 - Do unicode file recognition:  false
2024-12-26 16:53:51,650 - Do unicode file recognition:  false
2024-12-26 16:53:51,650 - FileResourceLoader : adding path '.'
2024-12-26 16:53:51,650 - FileResourceLoader : adding path '.'
2024-12-26 16:53:51,650 - FileResourceLoader : adding path '.'
2024-12-26 16:53:51,650 - FileResourceLoader : adding path '.'
2024-12-26 16:53:51,650 - FileResourceLoader : adding path '.'
2024-12-26 16:53:51,650 - FileResourceLoader : adding path '.'
2024-12-26 16:53:51,650 - FileResourceLoader : adding path '.'
2024-12-26 16:53:51,650 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:53:51,650 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:53:51,650 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:53:51,650 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:53:51,650 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:53:51,650 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:53:51,650 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:53:51,650 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:53:51,651 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2024-12-26 16:53:51,652 - Created '20' parsers.
2024-12-26 16:53:51,652 - Created '20' parsers.
2024-12-26 16:53:51,652 - Created '20' parsers.
2024-12-26 16:53:51,652 - Created '20' parsers.
2024-12-26 16:53:51,652 - Created '20' parsers.
2024-12-26 16:53:51,652 - Created '20' parsers.
2024-12-26 16:53:51,652 - Created '20' parsers.
2024-12-26 16:53:51,652 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:53:51,652 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:53:51,652 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:53:51,652 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:53:51,652 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:53:51,652 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:53:51,652 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2024-12-26 16:53:51,652 - Velocimacro : Default library not found.
2024-12-26 16:53:51,652 - Velocimacro : Default library not found.
2024-12-26 16:53:51,652 - Velocimacro : Default library not found.
2024-12-26 16:53:51,652 - Velocimacro : Default library not found.
2024-12-26 16:53:51,652 - Velocimacro : Default library not found.
2024-12-26 16:53:51,652 - Velocimacro : Default library not found.
2024-12-26 16:53:51,652 - Velocimacro : Default library not found.
2024-12-26 16:53:51,652 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:53:51,652 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:53:51,652 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:53:51,652 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:53:51,652 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:53:51,652 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:53:51,652 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2024-12-26 16:53:51,652 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:53:51,652 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:53:51,652 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:53:51,652 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:53:51,652 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:53:51,652 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:53:51,652 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2024-12-26 16:53:51,652 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:53:51,652 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:53:51,652 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:53:51,652 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:53:51,652 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:53:51,652 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:53:51,652 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2024-12-26 16:53:51,652 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:53:51,652 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:53:51,652 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:53:51,652 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:53:51,652 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:53:51,652 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 16:53:51,652 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2024-12-26 17:05:14,175 - Log4JLogChute initialized using file 'velocity.log'
